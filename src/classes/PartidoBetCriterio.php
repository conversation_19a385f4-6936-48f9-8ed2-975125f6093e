<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a partido bet criterio record.
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class PartidoBetCriterio
{
    // --- Attributes ---
    private ?string $id = null; // Stores the 'desordenado' ID
    private ?string $id_partido_bet_detalle = null;
    private ?string $criterio_nombre_1 = null;
    private ?float $criterio_valor_1 = null;
    private ?int $criterio_cumplido_1 = null;
    private ?string $criterio_nombre_2 = null;
    private ?float $criterio_valor_2 = null;
    private ?int $criterio_cumplido_2 = null;
    private ?string $criterio_nombre_3 = null;
    private ?float $criterio_valor_3 = null;
    private ?int $criterio_cumplido_3 = null;
    private ?string $criterio_nombre_4 = null;
    private ?float $criterio_valor_4 = null;
    private ?int $criterio_cumplido_4 = null;
    private ?string $criterio_nombre_5 = null;
    private ?float $criterio_valor_5 = null;
    private ?int $criterio_cumplido_5 = null;
    private ?string $criterio_nombre_6 = null;
    private ?float $criterio_valor_6 = null;
    private ?int $criterio_cumplido_6 = null;
    private ?string $criterio_nombre_7 = null;
    private ?float $criterio_valor_7 = null;
    private ?int $criterio_cumplido_7 = null;
    private ?string $criterio_nombre_8 = null;
    private ?float $criterio_valor_8 = null;
    private ?int $criterio_cumplido_8 = null;
    private ?string $criterio_nombre_9 = null;
    private ?float $criterio_valor_9 = null;
    private ?int $criterio_cumplido_9 = null;
    private ?string $criterio_nombre_10 = null;
    private ?float $criterio_valor_10 = null;
    private ?int $criterio_cumplido_10 = null;
    private ?string $criterio_nombre_11 = null;
    private ?float $criterio_valor_11 = null;
    private ?int $criterio_cumplido_11 = null;
    private ?string $criterio_nombre_12 = null;
    private ?float $criterio_valor_12 = null;
    private ?int $criterio_cumplido_12 = null;

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->id_partido_bet_detalle = null;
        $this->criterio_nombre_1 = null;
        $this->criterio_valor_1 = null;
        $this->criterio_cumplido_1 = null;
        $this->criterio_nombre_2 = null;
        $this->criterio_valor_2 = null;
        $this->criterio_cumplido_2 = null;
        $this->criterio_nombre_3 = null;
        $this->criterio_valor_3 = null;
        $this->criterio_cumplido_3 = null;
        $this->criterio_nombre_4 = null;
        $this->criterio_valor_4 = null;
        $this->criterio_cumplido_4 = null;
        $this->criterio_nombre_5 = null;
        $this->criterio_valor_5 = null;
        $this->criterio_cumplido_5 = null;
        $this->criterio_nombre_6 = null;
        $this->criterio_valor_6 = null;
        $this->criterio_cumplido_6 = null;
        $this->criterio_nombre_7 = null;
        $this->criterio_valor_7 = null;
        $this->criterio_cumplido_7 = null;
        $this->criterio_nombre_8 = null;
        $this->criterio_valor_8 = null;
        $this->criterio_cumplido_8 = null;
        $this->criterio_nombre_9 = null;
        $this->criterio_valor_9 = null;
        $this->criterio_cumplido_9 = null;
        $this->criterio_nombre_10 = null;
        $this->criterio_valor_10 = null;
        $this->criterio_cumplido_10 = null;
        $this->criterio_nombre_11 = null;
        $this->criterio_valor_11 = null;
        $this->criterio_cumplido_11 = null;
        $this->criterio_nombre_12 = null;
        $this->criterio_valor_12 = null;
        $this->criterio_cumplido_12 = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of PartidoBetCriterio.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                    error_log("desordena() returned empty/null for DB ID: " . $dbId);
                    throw new Exception("Error processing PartidoBetCriterio ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }

            // Handle id_partido_bet_detalle
            $dbIdPartidoBetDetalle = $data['id_partido_bet_detalle'] ?? null;
            if ($dbIdPartidoBetDetalle !== null) {
                $desordenadoIdPartidoBetDetalle = desordena((string)$dbIdPartidoBetDetalle);
                $objeto->id_partido_bet_detalle = $desordenadoIdPartidoBetDetalle;
            } else {
                $objeto->id_partido_bet_detalle = null;
            }

            // Handle all criterio fields
            for ($i = 1; $i <= 12; $i++) {
                $nombreField = "criterio_nombre_$i";
                $valorField = "criterio_valor_$i";
                $cumplidoField = "criterio_cumplido_$i";

                $objeto->$nombreField = $data[$nombreField] ?? null;
                $objeto->$valorField = isset($data[$valorField]) ? (float)$data[$valorField] : null;
                $objeto->$cumplidoField = isset($data[$cumplidoField]) ? (int)$data[$cumplidoField] : null;
            }

            return $objeto;

        } catch (Exception $e) {
            throw new Exception("Failed to construct PartidoBetCriterio from data: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a PartidoBetCriterio object from the database by its 'desordenado' string ID.
     *
     * @param string $id The 'desordenado' string ID of the PartidoBetCriterio to retrieve.
     * @param PDO $conexion The database connection object.
     * @return self|null A PartidoBetCriterio object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM partidos_bets_criterios
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado === false) {
                return null; // No record found
            }

            return self::construct($resultado);

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBetCriterio: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all PartidoBetCriterio objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of PartidoBetCriterio objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM partidos_bets_criterios
            ORDER BY id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $partidoBetCriterios = [];
            foreach ($resultados as $resultado) {
                $partidoBetCriterios[] = self::construct($resultado);
            }

            return $partidoBetCriterios;

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBetCriterio list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of PartidoBetCriterio objects by partido bet detalle ID.
     *
     * @param string $idPartidoBetDetalle The 'desordenado' string ID of the partido bet detalle.
     * @param PDO $conexion The database connection object.
     * @return array An array of PartidoBetCriterio objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getByPartidoBetDetalle(string $idPartidoBetDetalle, PDO $conexion): array
    {
        if (empty(trim($idPartidoBetDetalle))) {
            throw new InvalidArgumentException("Invalid partido bet detalle ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idPartidoBetDetalleOrdenado = ordena($idPartidoBetDetalle);
            if ($idPartidoBetDetalleOrdenado === false || $idPartidoBetDetalleOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided partido bet detalle ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM partidos_bets_criterios
            WHERE id_partido_bet_detalle = :id_partido_bet_detalle
            ORDER BY id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_partido_bet_detalle', $idPartidoBetDetalleOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $partidoBetCriterios = [];
            foreach ($resultados as $resultado) {
                $partidoBetCriterios[] = self::construct($resultado);
            }

            return $partidoBetCriterios;

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBetCriterio list by partido bet detalle: " . $e->getMessage());
        }
    }

    /**
     * Saves (inserts or updates) the current PartidoBetCriterio instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("PartidoBetCriterio::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("PartidoBetCriterio::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            if ($isInsertOperation) {
                return $this->_insert($conexion);
            } else {
                return $this->_update($conexion);
            }
        } catch (PDOException $e) {
            error_log("PartidoBetCriterio::guardar() - PDO Error: " . $e->getMessage());
            throw new Exception("Database error during save operation: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current PartidoBetCriterio instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        $query = <<<SQL
        INSERT INTO partidos_bets_criterios (
            id_partido_bet_detalle,
            criterio_nombre_1, criterio_valor_1, criterio_cumplido_1,
            criterio_nombre_2, criterio_valor_2, criterio_cumplido_2,
            criterio_nombre_3, criterio_valor_3, criterio_cumplido_3,
            criterio_nombre_4, criterio_valor_4, criterio_cumplido_4,
            criterio_nombre_5, criterio_valor_5, criterio_cumplido_5,
            criterio_nombre_6, criterio_valor_6, criterio_cumplido_6,
            criterio_nombre_7, criterio_valor_7, criterio_cumplido_7,
            criterio_nombre_8, criterio_valor_8, criterio_cumplido_8,
            criterio_nombre_9, criterio_valor_9, criterio_cumplido_9,
            criterio_nombre_10, criterio_valor_10, criterio_cumplido_10,
            criterio_nombre_11, criterio_valor_11, criterio_cumplido_11,
            criterio_nombre_12, criterio_valor_12, criterio_cumplido_12
        ) VALUES (
            :id_partido_bet_detalle,
            :criterio_nombre_1, :criterio_valor_1, :criterio_cumplido_1,
            :criterio_nombre_2, :criterio_valor_2, :criterio_cumplido_2,
            :criterio_nombre_3, :criterio_valor_3, :criterio_cumplido_3,
            :criterio_nombre_4, :criterio_valor_4, :criterio_cumplido_4,
            :criterio_nombre_5, :criterio_valor_5, :criterio_cumplido_5,
            :criterio_nombre_6, :criterio_valor_6, :criterio_cumplido_6,
            :criterio_nombre_7, :criterio_valor_7, :criterio_cumplido_7,
            :criterio_nombre_8, :criterio_valor_8, :criterio_cumplido_8,
            :criterio_nombre_9, :criterio_valor_9, :criterio_cumplido_9,
            :criterio_nombre_10, :criterio_valor_10, :criterio_cumplido_10,
            :criterio_nombre_11, :criterio_valor_11, :criterio_cumplido_11,
            :criterio_nombre_12, :criterio_valor_12, :criterio_cumplido_12
        )
        SQL;

        $statement = $conexion->prepare($query);

        // Convert foreign key ID to ordenado for database storage
        $idPartidoBetDetalleOrdenado = null;
        if ($this->getIdPartidoBetDetalle() !== null) {
            $idPartidoBetDetalleOrdenado = ordena($this->getIdPartidoBetDetalle());
            if ($idPartidoBetDetalleOrdenado === false || $idPartidoBetDetalleOrdenado <= 0) {
                throw new Exception("Failed to process the partido bet detalle ID for insert: " . $this->getIdPartidoBetDetalle());
            }
        }

        $statement->bindValue(':id_partido_bet_detalle', $idPartidoBetDetalleOrdenado, PDO::PARAM_INT);

        // Bind all criterio fields
        for ($i = 1; $i <= 12; $i++) {
            $nombreMethod = "getCriterioNombre$i";
            $valorMethod = "getCriterioValor$i";
            $cumplidoMethod = "getCriterioCumplido$i";

            $statement->bindValue(":criterio_nombre_$i", $this->$nombreMethod(), PDO::PARAM_STR);
            $statement->bindValue(":criterio_valor_$i", $this->$valorMethod(), PDO::PARAM_STR);
            $statement->bindValue(":criterio_cumplido_$i", $this->$cumplidoMethod(), PDO::PARAM_INT);
        }

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID and convert it to 'desordenado' format
            $lastInsertId = $conexion->lastInsertId();
            $desordenadoId = desordena($lastInsertId);
            if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                error_log("desordena() returned empty/null for inserted ID: " . $lastInsertId);
                throw new Exception("Error processing inserted PartidoBetCriterio ID. desordena() failed.");
            }
            $this->setId($desordenadoId);
            error_log("PartidoBetCriterio inserted successfully with ID: " . $this->getId());
        } else {
            error_log("Failed to insert PartidoBetCriterio: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current PartidoBetCriterio instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update PartidoBetCriterio without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
        if ($idOrdenado === false || $idOrdenado <= 0) {
            throw new Exception("Failed to process the PartidoBetCriterio ID for update: " . $this->getId());
        }

        $query = <<<SQL
        UPDATE partidos_bets_criterios SET
             id_partido_bet_detalle = :id_partido_bet_detalle
            ,criterio_nombre_1 = :criterio_nombre_1, criterio_valor_1 = :criterio_valor_1, criterio_cumplido_1 = :criterio_cumplido_1
            ,criterio_nombre_2 = :criterio_nombre_2, criterio_valor_2 = :criterio_valor_2, criterio_cumplido_2 = :criterio_cumplido_2
            ,criterio_nombre_3 = :criterio_nombre_3, criterio_valor_3 = :criterio_valor_3, criterio_cumplido_3 = :criterio_cumplido_3
            ,criterio_nombre_4 = :criterio_nombre_4, criterio_valor_4 = :criterio_valor_4, criterio_cumplido_4 = :criterio_cumplido_4
            ,criterio_nombre_5 = :criterio_nombre_5, criterio_valor_5 = :criterio_valor_5, criterio_cumplido_5 = :criterio_cumplido_5
            ,criterio_nombre_6 = :criterio_nombre_6, criterio_valor_6 = :criterio_valor_6, criterio_cumplido_6 = :criterio_cumplido_6
            ,criterio_nombre_7 = :criterio_nombre_7, criterio_valor_7 = :criterio_valor_7, criterio_cumplido_7 = :criterio_cumplido_7
            ,criterio_nombre_8 = :criterio_nombre_8, criterio_valor_8 = :criterio_valor_8, criterio_cumplido_8 = :criterio_cumplido_8
            ,criterio_nombre_9 = :criterio_nombre_9, criterio_valor_9 = :criterio_valor_9, criterio_cumplido_9 = :criterio_cumplido_9
            ,criterio_nombre_10 = :criterio_nombre_10, criterio_valor_10 = :criterio_valor_10, criterio_cumplido_10 = :criterio_cumplido_10
            ,criterio_nombre_11 = :criterio_nombre_11, criterio_valor_11 = :criterio_valor_11, criterio_cumplido_11 = :criterio_cumplido_11
            ,criterio_nombre_12 = :criterio_nombre_12, criterio_valor_12 = :criterio_valor_12, criterio_cumplido_12 = :criterio_cumplido_12
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        // Convert foreign key ID to ordenado for database storage
        $idPartidoBetDetalleOrdenado = null;
        if ($this->getIdPartidoBetDetalle() !== null) {
            $idPartidoBetDetalleOrdenado = ordena($this->getIdPartidoBetDetalle());
            if ($idPartidoBetDetalleOrdenado === false || $idPartidoBetDetalleOrdenado <= 0) {
                throw new Exception("Failed to process the partido bet detalle ID for update: " . $this->getIdPartidoBetDetalle());
            }
        }

        $statement->bindValue(':id_partido_bet_detalle', $idPartidoBetDetalleOrdenado, PDO::PARAM_INT);

        // Bind all criterio fields
        for ($i = 1; $i <= 12; $i++) {
            $nombreMethod = "getCriterioNombre$i";
            $valorMethod = "getCriterioValor$i";
            $cumplidoMethod = "getCriterioCumplido$i";

            $statement->bindValue(":criterio_nombre_$i", $this->$nombreMethod(), PDO::PARAM_STR);
            $statement->bindValue(":criterio_valor_$i", $this->$valorMethod(), PDO::PARAM_STR);
            $statement->bindValue(":criterio_cumplido_$i", $this->$cumplidoMethod(), PDO::PARAM_INT);
        }

        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT); // Bind the 'ordenado' ID

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update PartidoBetCriterio: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deletes (hard deletes) a PartidoBetCriterio record from the database.
     * Note: This table does not have an estado field, so this performs a hard deletion.
     *
     * @param string $id The 'desordenado' string ID of the PartidoBetCriterio to delete.
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            DELETE FROM partidos_bets_criterios
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Database error while deleting PartidoBetCriterio: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the PartidoBetCriterio.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
            throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            // Validate id_partido_bet_detalle (required)
            if ($this->getIdPartidoBetDetalle() === null || empty(trim($this->getIdPartidoBetDetalle()))) {
                throw new Exception('Debe especificar el ID del detalle del partido bet');
            }

            // Validate criterio values if they are set (all are optional)
            for ($i = 1; $i <= 12; $i++) {
                $valorMethod = "getCriterioValor$i";
                $setValorMethod = "setCriterioValor$i";

                $valorOriginal = $this->$valorMethod();
                if ($valorOriginal !== null) {
                    $valorLimpio = format_numberclean((string)$valorOriginal);
                    if (!is_numeric($valorLimpio)) {
                        throw new Exception("El criterio valor $i proporcionado no es numérico después de la limpieza.");
                    }
                    $this->$setValorMethod((float)$valorLimpio);
                }
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdPartidoBetDetalle(): ?string
    {
        return $this->id_partido_bet_detalle;
    }

    public function setIdPartidoBetDetalle(?string $id_partido_bet_detalle): self
    {
        $this->id_partido_bet_detalle = $id_partido_bet_detalle;
        return $this;
    }

    // Criterio 1
    public function getCriterioNombre1(): ?string
    {
        return $this->criterio_nombre_1;
    }

    public function setCriterioNombre1(?string $criterio_nombre_1): self
    {
        $this->criterio_nombre_1 = $criterio_nombre_1;
        return $this;
    }

    public function getCriterioValor1(): ?float
    {
        return $this->criterio_valor_1;
    }

    public function setCriterioValor1(?float $criterio_valor_1): self
    {
        $this->criterio_valor_1 = $criterio_valor_1;
        return $this;
    }

    // Criterio 2
    public function getCriterioNombre2(): ?string
    {
        return $this->criterio_nombre_2;
    }

    public function setCriterioNombre2(?string $criterio_nombre_2): self
    {
        $this->criterio_nombre_2 = $criterio_nombre_2;
        return $this;
    }

    public function getCriterioValor2(): ?float
    {
        return $this->criterio_valor_2;
    }

    public function setCriterioValor2(?float $criterio_valor_2): self
    {
        $this->criterio_valor_2 = $criterio_valor_2;
        return $this;
    }

    // Criterio 3
    public function getCriterioNombre3(): ?string
    {
        return $this->criterio_nombre_3;
    }

    public function setCriterioNombre3(?string $criterio_nombre_3): self
    {
        $this->criterio_nombre_3 = $criterio_nombre_3;
        return $this;
    }

    public function getCriterioValor3(): ?float
    {
        return $this->criterio_valor_3;
    }

    public function setCriterioValor3(?float $criterio_valor_3): self
    {
        $this->criterio_valor_3 = $criterio_valor_3;
        return $this;
    }

    // Criterio 4
    public function getCriterioNombre4(): ?string
    {
        return $this->criterio_nombre_4;
    }

    public function setCriterioNombre4(?string $criterio_nombre_4): self
    {
        $this->criterio_nombre_4 = $criterio_nombre_4;
        return $this;
    }

    public function getCriterioValor4(): ?float
    {
        return $this->criterio_valor_4;
    }

    public function setCriterioValor4(?float $criterio_valor_4): self
    {
        $this->criterio_valor_4 = $criterio_valor_4;
        return $this;
    }

    // Criterio 5
    public function getCriterioNombre5(): ?string
    {
        return $this->criterio_nombre_5;
    }

    public function setCriterioNombre5(?string $criterio_nombre_5): self
    {
        $this->criterio_nombre_5 = $criterio_nombre_5;
        return $this;
    }

    public function getCriterioValor5(): ?float
    {
        return $this->criterio_valor_5;
    }

    public function setCriterioValor5(?float $criterio_valor_5): self
    {
        $this->criterio_valor_5 = $criterio_valor_5;
        return $this;
    }

    // Criterio 6
    public function getCriterioNombre6(): ?string
    {
        return $this->criterio_nombre_6;
    }

    public function setCriterioNombre6(?string $criterio_nombre_6): self
    {
        $this->criterio_nombre_6 = $criterio_nombre_6;
        return $this;
    }

    public function getCriterioValor6(): ?float
    {
        return $this->criterio_valor_6;
    }

    public function setCriterioValor6(?float $criterio_valor_6): self
    {
        $this->criterio_valor_6 = $criterio_valor_6;
        return $this;
    }

    // Criterio 7
    public function getCriterioNombre7(): ?string
    {
        return $this->criterio_nombre_7;
    }

    public function setCriterioNombre7(?string $criterio_nombre_7): self
    {
        $this->criterio_nombre_7 = $criterio_nombre_7;
        return $this;
    }

    public function getCriterioValor7(): ?float
    {
        return $this->criterio_valor_7;
    }

    public function setCriterioValor7(?float $criterio_valor_7): self
    {
        $this->criterio_valor_7 = $criterio_valor_7;
        return $this;
    }

    // Criterio 8
    public function getCriterioNombre8(): ?string
    {
        return $this->criterio_nombre_8;
    }

    public function setCriterioNombre8(?string $criterio_nombre_8): self
    {
        $this->criterio_nombre_8 = $criterio_nombre_8;
        return $this;
    }

    public function getCriterioValor8(): ?float
    {
        return $this->criterio_valor_8;
    }

    public function setCriterioValor8(?float $criterio_valor_8): self
    {
        $this->criterio_valor_8 = $criterio_valor_8;
        return $this;
    }

    // Criterio 9
    public function getCriterioNombre9(): ?string
    {
        return $this->criterio_nombre_9;
    }

    public function setCriterioNombre9(?string $criterio_nombre_9): self
    {
        $this->criterio_nombre_9 = $criterio_nombre_9;
        return $this;
    }

    public function getCriterioValor9(): ?float
    {
        return $this->criterio_valor_9;
    }

    public function setCriterioValor9(?float $criterio_valor_9): self
    {
        $this->criterio_valor_9 = $criterio_valor_9;
        return $this;
    }

    // Criterio 10
    public function getCriterioNombre10(): ?string
    {
        return $this->criterio_nombre_10;
    }

    public function setCriterioNombre10(?string $criterio_nombre_10): self
    {
        $this->criterio_nombre_10 = $criterio_nombre_10;
        return $this;
    }

    public function getCriterioValor10(): ?float
    {
        return $this->criterio_valor_10;
    }

    public function setCriterioValor10(?float $criterio_valor_10): self
    {
        $this->criterio_valor_10 = $criterio_valor_10;
        return $this;
    }

    // Criterio 11
    public function getCriterioNombre11(): ?string
    {
        return $this->criterio_nombre_11;
    }

    public function setCriterioNombre11(?string $criterio_nombre_11): self
    {
        $this->criterio_nombre_11 = $criterio_nombre_11;
        return $this;
    }

    public function getCriterioValor11(): ?float
    {
        return $this->criterio_valor_11;
    }

    public function setCriterioValor11(?float $criterio_valor_11): self
    {
        $this->criterio_valor_11 = $criterio_valor_11;
        return $this;
    }

    // Criterio 12
    public function getCriterioNombre12(): ?string
    {
        return $this->criterio_nombre_12;
    }

    public function setCriterioNombre12(?string $criterio_nombre_12): self
    {
        $this->criterio_nombre_12 = $criterio_nombre_12;
        return $this;
    }

    public function getCriterioValor12(): ?float
    {
        return $this->criterio_valor_12;
    }

    public function setCriterioValor12(?float $criterio_valor_12): self
    {
        $this->criterio_valor_12 = $criterio_valor_12;
        return $this;
    }

    // Criterio Cumplido 1
    public function getCriterioCumplido1(): ?int
    {
        return $this->criterio_cumplido_1;
    }

    public function setCriterioCumplido1(?int $criterio_cumplido_1): self
    {
        $this->criterio_cumplido_1 = $criterio_cumplido_1;
        return $this;
    }

    // Criterio Cumplido 2
    public function getCriterioCumplido2(): ?int
    {
        return $this->criterio_cumplido_2;
    }

    public function setCriterioCumplido2(?int $criterio_cumplido_2): self
    {
        $this->criterio_cumplido_2 = $criterio_cumplido_2;
        return $this;
    }

    // Criterio Cumplido 3
    public function getCriterioCumplido3(): ?int
    {
        return $this->criterio_cumplido_3;
    }

    public function setCriterioCumplido3(?int $criterio_cumplido_3): self
    {
        $this->criterio_cumplido_3 = $criterio_cumplido_3;
        return $this;
    }

    // Criterio Cumplido 4
    public function getCriterioCumplido4(): ?int
    {
        return $this->criterio_cumplido_4;
    }

    public function setCriterioCumplido4(?int $criterio_cumplido_4): self
    {
        $this->criterio_cumplido_4 = $criterio_cumplido_4;
        return $this;
    }

    // Criterio Cumplido 5
    public function getCriterioCumplido5(): ?int
    {
        return $this->criterio_cumplido_5;
    }

    public function setCriterioCumplido5(?int $criterio_cumplido_5): self
    {
        $this->criterio_cumplido_5 = $criterio_cumplido_5;
        return $this;
    }

    // Criterio Cumplido 6
    public function getCriterioCumplido6(): ?int
    {
        return $this->criterio_cumplido_6;
    }

    public function setCriterioCumplido6(?int $criterio_cumplido_6): self
    {
        $this->criterio_cumplido_6 = $criterio_cumplido_6;
        return $this;
    }

    // Criterio Cumplido 7
    public function getCriterioCumplido7(): ?int
    {
        return $this->criterio_cumplido_7;
    }

    public function setCriterioCumplido7(?int $criterio_cumplido_7): self
    {
        $this->criterio_cumplido_7 = $criterio_cumplido_7;
        return $this;
    }

    // Criterio Cumplido 8
    public function getCriterioCumplido8(): ?int
    {
        return $this->criterio_cumplido_8;
    }

    public function setCriterioCumplido8(?int $criterio_cumplido_8): self
    {
        $this->criterio_cumplido_8 = $criterio_cumplido_8;
        return $this;
    }

    // Criterio Cumplido 9
    public function getCriterioCumplido9(): ?int
    {
        return $this->criterio_cumplido_9;
    }

    public function setCriterioCumplido9(?int $criterio_cumplido_9): self
    {
        $this->criterio_cumplido_9 = $criterio_cumplido_9;
        return $this;
    }

    // Criterio Cumplido 10
    public function getCriterioCumplido10(): ?int
    {
        return $this->criterio_cumplido_10;
    }

    public function setCriterioCumplido10(?int $criterio_cumplido_10): self
    {
        $this->criterio_cumplido_10 = $criterio_cumplido_10;
        return $this;
    }

    // Criterio Cumplido 11
    public function getCriterioCumplido11(): ?int
    {
        return $this->criterio_cumplido_11;
    }

    public function setCriterioCumplido11(?int $criterio_cumplido_11): self
    {
        $this->criterio_cumplido_11 = $criterio_cumplido_11;
        return $this;
    }

    // Criterio Cumplido 12
    public function getCriterioCumplido12(): ?int
    {
        return $this->criterio_cumplido_12;
    }

    public function setCriterioCumplido12(?int $criterio_cumplido_12): self
    {
        $this->criterio_cumplido_12 = $criterio_cumplido_12;
        return $this;
    }
}
